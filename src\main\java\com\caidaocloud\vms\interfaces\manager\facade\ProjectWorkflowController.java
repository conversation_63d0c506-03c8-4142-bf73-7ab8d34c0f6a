package com.caidaocloud.vms.interfaces.manager.facade;

import java.util.List;

import com.caidaocloud.vms.application.service.ProjectWorkflowService;
import com.caidaocloud.vms.application.service.WorkflowCallbackService;
import com.caidaocloud.vms.application.vo.ProjectContactVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.application.vo.ProjectSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.vo.WorkflowContactDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowPositionDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSettingDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSupplierDetailVO;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流回调Controller
 * 处理工作流审批结果的回调
 *
 * <AUTHOR> Zhou
 * @date 2025/10/17
 */
@RestController
@RequestMapping("/api/vms/v1/project/workflow")
@Api(tags = "工作流管理")
@Slf4j
public class ProjectWorkflowController {

    @Autowired
    private WorkflowCallbackService workflowCallbackService;

    @Autowired
    private ProjectWorkflowService projectWorkflowService;

    /**
     * 项目工作流审批通过回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("approve")
    @ApiOperation(value = "项目工作流审批通过")
    public Result approveProjectWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("项目工作流审批通过回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
         return Result.ok();

    }

    /**
     * 项目工作流审批拒绝回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("reject")
    @ApiOperation(value = "项目工作流审批拒绝", notes = "工作流审批拒绝后的回调处理")
    public Result rejectProjectWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目工作流审批拒绝回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
         return Result.ok();

    }

    /**
     * 审批流程项目详情查询
     * 根据businessKey查询审批流程中的项目基本信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程项目详情信息
     */
    @GetMapping("project/detail")
    @ApiOperation(value = "获取审批流程项目详情", notes = "根据businessKey获取审批流程中的项目基本信息详情，包括变更记录")
    public Result<WorkflowDetailVO<ProjectVO>> loadWorkflowProjectDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<ProjectVO> vo = projectWorkflowService.loadWorkflowProjectDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程岗位详情信息
     */
    @GetMapping("position/detail")
    @ApiOperation(value = "获取审批流程岗位详情", notes = "根据businessKey获取审批流程中的岗位信息详情，包括变更记录")
    public Result<WorkflowDetailVO<List<ProjectPositionVO>>> loadWorkflowPositionDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<List<ProjectPositionVO>> vo = projectWorkflowService.loadWorkflowPositionDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程供应商详情查询
     * 根据businessKey查询审批流程中的供应商信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程供应商详情信息
     */
    @GetMapping("supplier/detail")
    @ApiOperation(value = "获取审批流程供应商详情", notes = "根据businessKey获取审批流程中的供应商信息详情，包括变更记录")
    public Result<WorkflowDetailVO<List<ProjectSupplierVO>> > loadWorkflowSupplierDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<List<ProjectSupplierVO>>  vo = projectWorkflowService.loadWorkflowSupplierDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程联系人详情查询
     * 根据businessKey查询审批流程中的联系人信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程联系人详情信息
     */
    @GetMapping("contact/detail")
    @ApiOperation(value = "获取审批流程联系人详情", notes = "根据businessKey获取审批流程中的联系人信息详情，包括变更记录")
    public Result<WorkflowDetailVO<List<ProjectContactVO>>> loadWorkflowContactDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<List<ProjectContactVO>> vo = projectWorkflowService.loadWorkflowContactDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程设置详情查询
     * 根据businessKey查询审批流程中的设置信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程设置详情信息
     */
    @GetMapping("setting/detail")
    @ApiOperation(value = "获取审批流程设置详情", notes = "根据businessKey获取审批流程中的设置信息详情，包括变更记录")
    public Result<WorkflowDetailVO<ProjectSettingVO>> loadWorkflowSettingDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<ProjectSettingVO> vo = projectWorkflowService.loadWorkflowSettingDetail(businessKey);
        return Result.ok(vo);
    }

}
