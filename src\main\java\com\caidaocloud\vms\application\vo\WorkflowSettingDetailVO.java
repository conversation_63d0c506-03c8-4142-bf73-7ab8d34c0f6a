package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审批流程设置详情VO
 * 
 * <AUTHOR>
 * @date 2025/12/25
 */
@Data
@ApiModel(description = "审批流程设置详情信息")
public class WorkflowSettingDetailVO {
    
    @ApiModelProperty(value = "历史记录ID")
    private String historyId;
    
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    @ApiModelProperty(value = "提交人")
    private EmpSimple submitter;
    
    @ApiModelProperty(value = "审批人")
    private EmpSimple approver;
    
    @ApiModelProperty(value = "提交时间")
    private Long submitTime;
    
    @ApiModelProperty(value = "审批时间")
    private Long approveTime;
    
    @ApiModelProperty(value = "变更记录列表")
    private List<ProjectChange> changes;
    
    @ApiModelProperty(value = "设置详细信息")
    private ProjectSettingVO settingDetail;
}
