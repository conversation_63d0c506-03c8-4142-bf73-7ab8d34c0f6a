package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.application.service.ProjectService;
import com.caidaocloud.vms.application.vo.ProjectSimpleVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.vo.WorkflowProjectDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowPositionDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSupplierDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowContactDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSettingDetailVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目管理控制器
 * 
 * <AUTHOR> Zhou
 * @date 2025/6/3
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project")
@Api(tags = "项目管理", description = "项目信息的增删改查接口")
@Validated
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    /**
     * 保存项目
     * 
     * @param projectDto 项目信息
     * @return 操作结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增项目", notes = "创建新的项目信息")
    public Result saveProject(@ApiParam(value = "项目信息", required = true) @RequestBody ProjectDto projectDto) {

        // 手动校验必填字段
        if (projectDto.getProjectCode() == null || projectDto.getProjectCode().trim().isEmpty()) {
            return Result.fail("项目编码不能为空");
        }
        if (projectDto.getProjectName() == null || projectDto.getProjectName().trim().isEmpty()) {
            return Result.fail("项目名称不能为空");
        }

        projectService.save(projectDto);
        return Result.ok();

    }

    /**
     * 保存项目
     *
     * @param projectDto 项目信息
     * @return 操作结果
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑项目基本信息", notes = "创建新的项目信息")
    public Result editProject(@ApiParam(value = "项目信息", required = true) @RequestBody ProjectDto projectDto) {
        projectService.edit(projectDto);
        return Result.ok();

    }

    /**
     * 开始项目
     *
     * @param projectDto 项目信息
     * @return 操作结果
     */
    @PostMapping("/start")
    @ApiOperation(value = "开始项目", notes = "开始项目")
    public Result startProject(@ApiParam(value = "项目信息", required = true) @RequestBody ProjectDto projectDto) {
        projectService.start(projectDto);
        return Result.ok();
    }

    /**
     * 结束项目
     *
     * @param projectDto 项目信息
     * @return 操作结果
     */
    @PostMapping("/end")
    @ApiOperation(value = "结束项目", notes = "结束项目")
    public Result endProject(@ApiParam(value = "项目信息", required = true) @RequestBody ProjectDto projectDto) {
        projectService.end(projectDto);
        return Result.ok();
    }

    /**
     * 保存项目
     *
     * @param projectDto 项目信息
     * @return 操作结果
     */
    @PostMapping("/delete")
    @ApiOperation(value = "编辑项目基本信息", notes = "创建新的项目信息")
    public Result deleteProject(@RequestBody ProjectDto projectDto) {
        projectService.delete(projectDto);
        return Result.ok();

    }

    /**
     * 提交项目变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit")
    @ApiOperation(value = "提交项目变更", notes = "提交项目的所有变更，生成历史记录并发起工作流")
    public Result commitProject(
            @RequestBody ProjectDto projectDto) {

        projectService.commitProject(projectDto);
        return Result.ok();

    }

    /**
     * 分页查询项目列表
     *
     * @param queryDTO 查询条件
     * @return 项目列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询项目列表", notes = "根据查询条件获取项目分页列表")
    public Result<PageResult<ProjectVO>> projectPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody ProjectQueryDTO queryDTO) {

        PageResult<ProjectVO> result = projectService.projectPage(queryDTO);
        return Result.ok(result);

    }

    /**
     * 根据ID加载项目基础信息
     *
     * @param projectId 项目ID
     * @return 项目详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取项目详情", notes = "根据ID获取项目的详细信息")
    public Result<ProjectVO> loadProject(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        ProjectVO vo = projectService.loadProject(projectId);
        return Result.ok(vo);

    }

    /**
     * 审批流程项目详情查询
     * 根据businessKey查询审批流程中的项目基本信息详情
     *
     * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
     * @return 审批流程项目详情信息
     */
    @GetMapping("/workflow/project/detail")
    @ApiOperation(value = "获取审批流程项目详情", notes = "根据businessKey获取审批流程中的项目基本信息详情，包括变更记录")
    public Result<WorkflowProjectDetailVO> loadWorkflowProjectDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowProjectDetailVO vo = projectService.loadWorkflowProjectDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位信息详情
     *
     * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
     * @return 审批流程岗位详情信息
     */
    @GetMapping("/workflow/position/detail")
    @ApiOperation(value = "获取审批流程岗位详情", notes = "根据businessKey获取审批流程中的岗位信息详情，包括变更记录")
    public Result<WorkflowPositionDetailVO> loadWorkflowPositionDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowPositionDetailVO vo = projectService.loadWorkflowPositionDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程供应商详情查询
     * 根据businessKey查询审批流程中的供应商信息详情
     *
     * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
     * @return 审批流程供应商详情信息
     */
    @GetMapping("/workflow/supplier/detail")
    @ApiOperation(value = "获取审批流程供应商详情", notes = "根据businessKey获取审批流程中的供应商信息详情，包括变更记录")
    public Result<WorkflowSupplierDetailVO> loadWorkflowSupplierDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowSupplierDetailVO vo = projectService.loadWorkflowSupplierDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程联系人详情查询
     * 根据businessKey查询审批流程中的联系人信息详情
     *
     * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
     * @return 审批流程联系人详情信息
     */
    @GetMapping("/workflow/contact/detail")
    @ApiOperation(value = "获取审批流程联系人详情", notes = "根据businessKey获取审批流程中的联系人信息详情，包括变更记录")
    public Result<WorkflowContactDetailVO> loadWorkflowContactDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowContactDetailVO vo = projectService.loadWorkflowContactDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程设置详情查询
     * 根据businessKey查询审批流程中的设置信息详情
     *
     * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
     * @return 审批流程设置详情信息
     */
    @GetMapping("/workflow/setting/detail")
    @ApiOperation(value = "获取审批流程设置详情", notes = "根据businessKey获取审批流程中的设置信息详情，包括变更记录")
    public Result<WorkflowSettingDetailVO> loadWorkflowSettingDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowSettingDetailVO vo = projectService.loadWorkflowSettingDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 获取当前供应商关联的项目下拉列表
     *
     * @return 项目简单信息列表
     */
    @GetMapping("/selectList")
    @ApiOperation(value = "获取供应商关联的项目列表", notes = "获取当前供应商关联的所有项目，用于下拉选择")
    public Result<List<ProjectSimpleVO>> getProjectsList() {

        List<ProjectSimpleVO> projects = projectService.loadProjectSelectList();
        return Result.ok(projects);
    }

}