package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.vms.application.dto.ProjectContactDto;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectSettingDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierDto;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.repository.ProjectContactRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSupplierRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 项目审批保护功能测试类
 * 验证在项目审批中状态时，各种业务数据修改操作被正确阻止
 *
 * <AUTHOR> Zhou
 * @date 2025/12/24
 */
@ExtendWith(MockitoExtension.class)
class ProjectApprovalProtectionTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private ProjectContactRepository projectContactRepository;

    @Mock
    private ProjectSupplierRepository projectSupplierRepository;

    @InjectMocks
    private ProjectService projectService;

    @InjectMocks
    private ProjectContactService projectContactService;

    @InjectMocks
    private ProjectSupplierService projectSupplierService;

    @InjectMocks
    private ProjectSettingService projectSettingService;

    private Project submittedProject;
    private ProjectDto projectDto;
    private ProjectContactDto contactDto;
    private ProjectSupplierDto supplierDto;
    private ProjectSettingDto settingDto;

    @BeforeEach
    void setUp() {
        // 创建审批中状态的项目
        submittedProject = new Project("TEST-001", "测试项目");
        submittedProject.setBid("project-123");
        submittedProject.setStatus(ProjectStatus.SUBMITTED.toEnumSimple());

        // 创建测试DTO
        projectDto = new ProjectDto();
        projectDto.setBid("project-123");
        projectDto.setProjectName("修改后的项目名称");

        contactDto = new ProjectContactDto();
        contactDto.setProjectId("project-123");
        contactDto.setEmpId("emp-123");

        supplierDto = new ProjectSupplierDto();
        supplierDto.setProjectId("project-123");
        supplierDto.setSupplierId("supplier-123");

        settingDto = new ProjectSettingDto();
        settingDto.setProjectId("project-123");
    }

    @Test
    @DisplayName("测试审批中项目不能编辑基础信息")
    void testCannotEditSubmittedProjectBasicInfo() {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectService.edit(projectDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试审批中项目不能添加联系人")
    void testCannotAddContactToSubmittedProject() {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectContactService.saveContact(contactDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试审批中项目不能编辑联系人")
    void testCannotEditContactOfSubmittedProject() {
        // Given
        ProjectContact contact = new ProjectContact("project-123", "emp-123", "<EMAIL>", "12345678901");
        contact.setBid("contact-123");
        
        contactDto.setBid("contact-123");
        
        when(projectContactRepository.getContact("contact-123")).thenReturn(Optional.of(contact));
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectContactService.editContact(contactDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试审批中项目不能添加供应商")
    void testCannotAddSupplierToSubmittedProject() {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectSupplierService.addProjectSupplier(supplierDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试审批中项目不能删除供应商")
    void testCannotDeleteSupplierFromSubmittedProject() {
        // Given
        ProjectSupplier supplier = new ProjectSupplier("project-123", "supplier-123");
        supplier.setBid("supplier-rel-123");
        
        supplierDto.setBid("supplier-rel-123");
        
        when(projectSupplierRepository.getById("supplier-rel-123")).thenReturn(supplier);
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectSupplierService.deleteProjectSupplier(supplierDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试审批中项目不能修改设置")
    void testCannotEditSettingOfSubmittedProject() {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(submittedProject);

        // When & Then
        ServerException exception = assertThrows(ServerException.class, 
            () -> projectSettingService.edit(settingDto));
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }
}
