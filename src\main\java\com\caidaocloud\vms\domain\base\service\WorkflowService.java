package com.caidaocloud.vms.domain.base.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.application.dto.base.WfTaskRevokeDTO;
import com.caidaocloud.vms.application.service.emp.EmpService;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;
import com.caidaocloud.vms.infrastructure.feign.IWfOperateFeignClient;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 工作流服务类
 * 负责判断是否需要走工作流审批流程
 *
 * <AUTHOR> Zhou
 * @date 2025/9/25
 */
@Service
@Slf4j
public class WorkflowService {

    @Autowired
    private IWfOperateFeignClient wfOperateFeignClient;

    @Autowired
    private IWfRegisterFeign wfRegisterFeign;

    @Autowired
    private EmpService empService;

    @Autowired
    private ProjectRepository projectRepository;

    /**
     * 启动工作流流程
     *
     * @param history 项目历史记录
     * @return
     */
    public String startWorkflow(ProjectHistory history) {
        String businessId = startWorkflow(history, WorkflowConfig.PROJECT_MANAGEMENT);
        updateProjectStatusToSubmitted(history.getProjectId(), businessId);
        return businessId;
    }

    /**
     * 启动工作流流程
     *  @param history 项目历史记录
     * @param wc workflowConfig
     * @return
     */
    public String startWorkflow(ProjectHistory history, WorkflowConfig wc) {
        log.info("WorkflowService: 启动工作流，  单据ID: {},funcode={}", history.getBid(), wc.getCode());
        EmpInfoDto emp = empService.loadEmp(String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()));

        // 启动工作流
        String businessId = doWorkflow(history.getBid(), emp, wc.getCode());

        // // 如果是项目管理工作流，修改项目状态为审批中
        // if (WorkflowConfig.PROJECT_MANAGEMENT==wc && history.getProjectId() != null) {
        //     updateProjectStatusToSubmitted(history.getProjectId(), history.getBid());
        // }
        return businessId;
    }

    /**
     * 启动员工变更记录工作流流程
     *
     * @param changeRecord 员工变更记录
     * @param funCode      funcode
     */
    public void startWorkflow(EmployeeChangeRecord changeRecord, String funCode) {
        log.info("WorkflowService: 启动员工变更工作流，单据ID: {}, funcode={}", changeRecord.getBid(), funCode);
        EmpInfoDto emp = empService.loadEmp(String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()));

        doWorkflow(changeRecord.getBid(), emp, funCode);

    }

    private String doWorkflow(String businessId, EmpInfoDto emp, String funCode) {
        // 根据实体类型和历史记录类型确定工作流流程
        WfBeginWorkflowDto beginDto = new WfBeginWorkflowDto();
        beginDto.setFuncCode(funCode);
        beginDto.setBusinessId(businessId);
        beginDto.setApplicantId(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()));
        beginDto.setApplicantName(emp.getName());
        // 业务单据事件时间为离职日期
        beginDto.setEventTime(System.currentTimeMillis());
        log.info("项目流程信息：" + FastjsonUtil.toJson(beginDto));
        Result wfResult = null;
        try {
            wfResult = wfRegisterFeign.begin(beginDto);
        } catch (Exception e) {
            log.error("流程发起失败,err msg={}", e.getMessage());
            throw new WorkflowStartException(e);
        }
        log.info("workflow result={}", wfResult);
        if (null != wfResult && !wfResult.isSuccess()) {
            throw new WorkflowStartException(wfResult.getMsg());
        }
        return businessId;
    }

    /**
     * 更新项目状态为审批中
     *
     * @param projectId          项目ID
     * @param workflowBusinessId 工作流业务ID
     */
    private void updateProjectStatusToSubmitted(String projectId, String workflowBusinessId) {
        try {
            Project project = projectRepository.getById(projectId);
            if (project != null) {
                project.submit(workflowBusinessId);
                projectRepository.saveOrUpdate(project);
                log.info("项目状态已更新为审批中，项目ID: {}, 工作流业务ID: {}", projectId, workflowBusinessId);
            } else {
                log.warn("未找到项目，无法更新状态，项目ID: {}", projectId);
            }
        } catch (Exception e) {
            log.error("更新项目状态失败，项目ID: {}, 工作流业务ID: {}", projectId, workflowBusinessId, e);
        }
    }

    public boolean checkWorkflowEnable() {
        try {
            Result<Boolean> result = wfOperateFeignClient.checkDefEnabled(WorkflowConfig.PROJECT_MANAGEMENT.getCode());
            if (null == result || !result.isSuccess()) {
                return false;
            }
            return (Boolean) result.getData();
        } catch (Exception e) {
            log.error("check work flow enable occurs error,fun code={}", (WorkflowConfig.PROJECT_MANAGEMENT.getCode()),
                    e);
            throw new ServerException("check work flow enable occurs error", e);
        }
    }

    /**
     * 注册工作流function和callback
     *
     * @param workflowConfig 工作流配置枚举
     */
    public void register(WorkflowConfig workflowConfig) {
        // 1. 先注册function
        registerFunction(workflowConfig);

        // 2. 再注册callback
        registerCallback(workflowConfig);
    }

    /**
     * 兼容旧版本的注册方法
     *
     * @param name 工作流名称
     * @param code 工作流代码
     */
    @Deprecated
    public void register(String name, String code) {
        try {
            WorkflowConfig config = WorkflowConfig.fromCode(code);
            register(config);
        } catch (IllegalArgumentException e) {
            // 如果枚举中没有找到对应配置，使用旧的注册方式
            log.warn("未找到工作流配置枚举，使用旧的注册方式: name={}, code={}", name, code);
            WfMetaFunDto dto = new WfMetaFunDto(name, code,
                    WfFunctionPageJumpType.RELATIVE_PATH, "",
                    "caidaocloud-vms-service",
                    "", "/api/vms/v1/project/detail", "", Lists.list());
            wfRegisterFeign.registerFunction(dto);
        }
    }

    /**
     * 注册工作流function
     *
     * @param workflowConfig 工作流配置
     */
    private void registerFunction(WorkflowConfig workflowConfig) {
        WfMetaFunDto dto = new WfMetaFunDto(
                workflowConfig.getName(),
                workflowConfig.getCode(),
                WfFunctionPageJumpType.RELATIVE_PATH,
                "",
                "caidaocloud-vms-service",
                "",
                workflowConfig.getDetailPath(),
                "",
                Lists.list());
        wfRegisterFeign.registerFunction(dto);
        log.info("注册工作流function成功: name={}, code={}", workflowConfig.getName(), workflowConfig.getCode());
    }

    /**
     * 注册工作流callback
     *
     * @param workflowConfig 工作流配置
     */
    private void registerCallback(WorkflowConfig workflowConfig) {
        for (WorkflowConfig.CallbackConfig callback : workflowConfig.getCallbacks()) {
            WfMetaCallbackDto dto = new WfMetaCallbackDto(
                    callback.getCallbackName(workflowConfig.getName()),
                    callback.getCallbackCode(),
                    Lists.list(workflowConfig.getCode()),
                    "",
                    callback.getCallbackAddress(),
                    "caidaocloud-vms-service",
                    "",
                    WfCallbackTypeEnum.RELATIVE_PATH,
                    WfCallbackTimeTypeEnum.NOW);
            wfRegisterFeign.registerCallback(dto);
            log.info("注册工作流callback成功: name={}, code={}, action={}",
                    callback.getCallbackName(workflowConfig.getName()),
                    callback.getCallbackCode(),
                    callback.getAction());
        }
    }

    public void revoke(String businessKey) {
        WfTaskRevokeDTO wfRevokeDto = new WfTaskRevokeDTO();
        wfRevokeDto.setBusinessKey(businessKey);
        try {
            Result<?> result = wfOperateFeignClient.revokeProcessOfTask(wfRevokeDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw new ServerException("撤销失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }
}
