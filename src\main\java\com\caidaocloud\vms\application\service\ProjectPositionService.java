package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.application.event.PositionSupplierRefreshEvent;
import com.caidaocloud.vms.application.event.ProjectBudgetRefreshEvent;
import com.caidaocloud.vms.application.service.emp.PostService;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.OrganizeService;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.PublishStatus;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.repository.*;

import org.springframework.beans.BeanUtils;

import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.PositionRequirementDto;
import com.caidaocloud.vms.application.dto.PositionSupplierDto;
import com.caidaocloud.vms.application.dto.PositionSupplierQueryDto;
import com.caidaocloud.vms.application.dto.ProjectPositionCreateDto;
import com.caidaocloud.vms.application.dto.ProjectPositionEditDto;
import com.caidaocloud.vms.application.dto.ProjectPositionQueryDTO;
import com.caidaocloud.vms.application.vo.PositionRequirementVO;
import com.caidaocloud.vms.application.vo.PositionSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectPositionPageVO;
import com.caidaocloud.vms.domain.project.entity.PositionRequirement;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.enums.DurationType;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import com.caidaocloud.vms.domain.supplier.repository.SupplierContactRepository;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Option;
import com.caidaocloud.vms.domain.project.dto.FormatAllResult;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 项目岗位服务
 *
 * <AUTHOR> Zhou
 * @date 2025/9/22
 */
@Service
@Slf4j
public class ProjectPositionService {
	@Autowired
	private ProjectRepository projectRepository;

	@Autowired
	private ProjectPositionRepository projectPositionRepository;

	@Autowired
	private PositionRequirementRepository positionRequirementRepository;

	@Autowired
	private ProjectSupplierRepository projectSupplierRepository;
	@Autowired
	private PositionSupplierRepository positionSupplierRepository;

	@Autowired
	private SupplierRepository supplierRepository;
	@Autowired
	private SupplierContactRepository supplierContactRepository;

	@Autowired
	private PostService postService;
	@Autowired
	private CompanyService companyService;
	@Autowired
	private OrganizeService organizeService;
	@Autowired
	private SupplierService supplierService;

	@Autowired
	private ProjectDraftRepository projectDraftRepository;

	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private ProjectHistoryFactory projectHistoryFactory;

	@Autowired
	private ProjectHistoryService projectHistoryService;

	@Autowired
	private ProjectSettingService projectSettingService;

	/**
	 * 新增项目岗位
	 * 传入岗位id、组织id以及公司id
	 *
	 * @param dto 岗位信息
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectPositionCreateDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.CREATE)
	public void savePosition(ProjectPositionCreateDto dto) {
		Project project = projectRepository.getById(dto.getProjectId());
		if (project == null) {
			throw new ServerException("Project not found: " + dto.getProjectId());
		}
		// 检查项目是否可以编辑
		project.checkUpdate();
		List<ProjectSupplier> supplierList = projectSupplierRepository.findByProjectId(project.getBid());
		Optional<PostInfoDto> post = postService.loadPost(dto.getPosition());
		if (!post.isPresent()) {
			throw new ServerException("Post not found: " + dto.getPosition());
		}
		// TODO: 2025/9/23 是否校验岗位重复

		ProjectPosition position = project.createPosition(dto.getPosition(), dto.getCompany(),
				dto.getOrganization());
		position.setPositionName(post.get().getName());
		position.setPositionCode(post.get().getCode());

		// 保存岗位
		String positionId = projectPositionRepository.saveOrUpdate(position);
		positionRequirementRepository.init(position.getPositionRequirement(), position.getBid());
		PositionSupplierRefreshEvent.positionCreateEvent(dto.getProjectId(), position.getBid()).publish();
	}

	/**
	 * 编辑项目岗位基本信息
	 *
	 * @param positionDto 岗位信息
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectPositionEditDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.UPDATE)
	public void editPosition(ProjectPositionEditDto positionDto) {
		if (positionDto.getBid() == null) {
			throw new ServerException("岗位ID不能为空");
		}

		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionDto.getBid());
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}

		ProjectPosition position = positionOpt.get();
		position.checkUpdate();

		// 检查项目是否可以编辑
		Project project = projectRepository.getById(position.getProjectId());
		if (project != null) {
			project.checkUpdate();
		}
		// TODO: 2025/11/6 校验项目预算总额

		position.updateBasicInfo(positionDto);

		// 保存更新
		projectPositionRepository.saveOrUpdate(position);
	}

	private void checkProvide(ProjectPosition position) {
		Project project = projectRepository.getById(position.getProjectId());

	}

	/**
	 * 获取项目岗位列表
	 *
	 * @param projectId 项目ID
	 * @return 岗位列表
	 */
	public List<ProjectPositionVO> getPositionList(String projectId) {
		List<ProjectPosition> positions = projectPositionRepository.loadPositionList(projectId);

		return positions.stream().map(position -> {
			ProjectPositionVO vo = new ProjectPositionVO();
			BeanUtils.copyProperties(position, vo);
			return vo;
		}).collect(Collectors.toList());
	}

	/**
	 * 获取岗位详情
	 *
	 * @param positionId 岗位ID
	 * @return 岗位详情
	 */
	public ProjectPositionVO getPositionDetail(String positionId) {
		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}
		Optional<PostInfoDto> dto = postService.loadPost(positionOpt.get().getPosition());
		ProjectPosition position = positionOpt.get();
		ProjectPositionVO vo = new ProjectPositionVO();
		BeanUtils.copyProperties(position, vo);
		vo.setDurationType(DurationType.fromValue(position.getDurationType()));
		vo.setEmploymentType(position.getEmploymentType().getValue());
		if (dto.isPresent()) {
			vo.setJobDescFiles(dto.get().getJobDescFiles());
		}
		return vo;
	}

	/**
	 * 删除项目岗位
	 *
	 * @param dto 岗位dto
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectPositionEditDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.DELETE)
	public void deletePosition(ProjectPositionEditDto dto) {
		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(dto.getBid());
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}
		// // 删除岗位相关的招聘要求
		// positionRequirementRepository.deleteByPositionId(positionId);
		//
		// // 删除岗位相关的供应商关系
		// positionSupplierRepository.deleteByPositionId(positionId);

		// 删除岗位
		projectPositionRepository.deletePosition(positionOpt.get());
	}

	// ==================== 岗位招聘要求相关方法 ====================

	/**
	 * 保存或更新岗位招聘要求
	 *
	 * @param requirementDto 招聘要求信息
	 */
	@PaasTransactional
	public void editRequirement(PositionRequirementDto requirementDto) {
		Optional<PositionRequirement> optional = positionRequirementRepository
				.getByPositionId(requirementDto.getPositionId());
		if (!optional.isPresent()) {
			throw new ServerException("PositionRequirement not found: " + requirementDto.getPositionId());
		}
		PositionRequirement requirement = optional.get();
		requirement.update(requirementDto);

		// 保存要求
		positionRequirementRepository.saveOrUpdate(requirement);
	}

	/**
	 * 获取岗位招聘要求
	 *
	 * @param positionId 岗位ID
	 * @return 招聘要求
	 */
	public PositionRequirementVO getPositionRequirement(String positionId) {
		Optional<PositionRequirement> requirementOpt = positionRequirementRepository.getByPositionId(positionId);
		if (!requirementOpt.isPresent()) {
			return null;
		}

		PositionRequirement requirement = requirementOpt.get();
		PositionRequirementVO vo = new PositionRequirementVO();
		BeanUtils.copyProperties(requirement, vo);
		return vo;
	}

	// ==================== 岗位供应商关系相关方法 ====================

	/**
	 * 添加岗位供应商关系
	 *
	 * @param supplierDto 供应商关系信息
	 */
	@PaasTransactional
	public void addPositionSupplier(PositionSupplierDto supplierDto) {
		// 检查关系是否已存在
		if (positionSupplierRepository.existsByPositionIdAndSupplierId(
				supplierDto.getPositionId(), supplierDto.getSupplierId())) {
			throw new ServerException("该岗位已关联此供应商");
		}

		// 如果设置了提供人数，需要校验总人数不超过岗位计划人数
		if (supplierDto.getProvide() != null) {
			validateTotalProvideCount(supplierDto.getPositionId(), null, supplierDto.getProvide());
		}

		// 创建新的关系
		PositionSupplier positionSupplier = new PositionSupplier(
				supplierDto.getProjectId(),
				supplierDto.getPositionId(),
				supplierDto.getSupplierId(),
				supplierDto.getSupplierContact(),
				supplierDto.getProvide());
		positionSupplierRepository.saveOrUpdate(positionSupplier);
	}

	/**
	 * 获取岗位的供应商列表（支持查询条件）
	 *
	 * @param queryDto 查询条件
	 * @return 供应商列表
	 */
	public List<PositionSupplierVO> getPositionSuppliers(PositionSupplierQueryDto queryDto) {
		List<PositionSupplier> relations = positionSupplierRepository.getByQuery(queryDto);

		if (relations.isEmpty()) {
			return new ArrayList<>();
		}

		// 批量查询供应商信息
		List<String> supplierIds = relations.stream()
				.map(PositionSupplier::getSupplierId)
				.distinct()
				.collect(Collectors.toList());

		List<Supplier> suppliers = supplierRepository.list(supplierIds);
		Map<String, Supplier> supplierMap = suppliers.stream()
				.collect(Collectors.toMap(Supplier::getBid, supplier -> supplier));

		// 批量查询供应商联系人信息
		List<SupplierContact> contactList = supplierService.loadContact(Sequences.sequence(relations)
				.flatMap(PositionSupplier::getSupplierContact).toList());

		// 组装VO并应用供应商名称过滤
		List<PositionSupplierVO> result = relations.stream()
				.map(relation -> {
					PositionSupplierVO vo = ObjectConverter.convert(relation, PositionSupplierVO.class);

					// 设置供应商信息
					Supplier supplier = supplierMap.get(relation.getSupplierId());
					if (supplier != null) {
						vo.setSupplierCode(supplier.getSupplierCode());
						vo.setSupplierName(supplier.getSupplierName());
					}

					// 设置供应商联系人信息
					if (CollectionUtils.isNotEmpty(relation.getSupplierContact())) {
						List<String> contactNameList = new ArrayList<>();
						for (String contactId : relation.getSupplierContact()) {
							Option<SupplierContact> option = Sequences.sequence(contactList)
									.find(c -> contactId.equals(c.getBid()));
							if (option.isDefined()) {
								SupplierContact contact = option.get();
								contactNameList.add(contact.getContact());
							}
						}

						vo.setSupplierContactName(StringUtils.join(contactNameList, ","));
					}

					return vo;
				})
				.collect(Collectors.toList());
		return result;
	}

	/**
	 * 获取岗位的供应商列表（简化版本，保持向后兼容）
	 *
	 * @param positionId 岗位ID
	 * @return 供应商列表
	 */
	public List<PositionSupplierVO> getPositionSuppliers(String positionId) {
		PositionSupplierQueryDto queryDto = new PositionSupplierQueryDto();
		queryDto.setPositionId(positionId);
		return getPositionSuppliers(queryDto);
	}

	/**
	 * 删除岗位供应商关系
	 *
	 * @param relationId 关系ID
	 */
	@PaasTransactional
	public void removePositionSupplier(String relationId) {
		Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(relationId);
		if (!relationOpt.isPresent()) {
			throw new ServerException("岗位供应商关系不存在");
		}
		positionSupplierRepository.deleteRelation(relationOpt.get());
	}

	/**
	 * 停用岗位供应商关系，将应邀状态改为终止
	 *
	 * @param relationId 关系ID
	 */
	@PaasTransactional
	public void terminatePositionSupplier(String relationId) {
		Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(relationId);
		if (!relationOpt.isPresent()) {
			throw new ServerException("岗位供应商关系不存在");
		}

		PositionSupplier relation = relationOpt.get();
		relation.terminate(); // 调用实体方法将状态改为终止
		positionSupplierRepository.saveOrUpdate(relation);
	}

	/**
	 * 更新岗位供应商报价信息
	 *
	 * @param supplierDto 供应商报价信息
	 */
	@PaasTransactional
	public void updatePositionSupplier(PositionSupplierDto supplierDto) {
		if (supplierDto.getBid() == null) {
			throw new ServerException("关系ID不能为空");
		}

		Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(supplierDto.getBid());
		if (!relationOpt.isPresent()) {
			throw new ServerException("岗位供应商关系不存在");
		}

		PositionSupplier relation = relationOpt.get();

		// 如果更新了提供人数，需要校验总人数不超过岗位计划人数
		if (supplierDto.getProvide() != null) {
			validateTotalProvideCount(relation.getPositionId(), supplierDto.getBid(), supplierDto.getProvide());
		}

		relation.update(supplierDto);

		positionSupplierRepository.saveOrUpdate(relation);
	}

	/**
	 * 校验所有供应商的提供人数总和不超过岗位的计划人数
	 *
	 * @param positionId        岗位ID
	 * @param excludeRelationId 排除的关系ID（用于更新时排除当前记录）
	 * @param newProvideCount   新的提供人数
	 */
	private void validateTotalProvideCount(String positionId, String excludeRelationId, Integer newProvideCount) {
		// 获取岗位信息
		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}

		ProjectPosition position = positionOpt.get();
		Integer plannedHeadcount = position.getPlannedHeadcount();
		if (plannedHeadcount == null || plannedHeadcount <= 0) {
			throw new ServerException("岗位计划人数未设置或无效");
		}

		// 获取该岗位的所有供应商关系
		List<PositionSupplier> allSuppliers = positionSupplierRepository.getByPositionId(positionId);

		// 计算当前总提供人数（排除要更新的记录）
		int totalProvideCount = 0;
		for (PositionSupplier supplier : allSuppliers) {
			// 排除当前要更新的记录
			if (excludeRelationId != null && excludeRelationId.equals(supplier.getBid())) {
				continue;
			}
			if (supplier.getProvide() != null) {
				totalProvideCount += supplier.getProvide();
			}
		}

		// 加上新的提供人数
		if (newProvideCount != null) {
			totalProvideCount += newProvideCount;
		}

		// 校验总人数不超过计划人数
		if (totalProvideCount > plannedHeadcount) {
			throw new ServerException(String.format("所有供应商提供人数总和(%d)不能超过岗位计划人数(%d)",
					totalProvideCount, plannedHeadcount));
		}
	}

	public PageResult<ProjectPositionPageVO> getPositionPage(ProjectPositionQueryDTO queryDTO) {
		PageResult<ProjectPosition> result = projectPositionRepository.pagePosition(queryDTO.getProjectId(),
				queryDTO.getPositionName(), queryDTO.getPageSize(), queryDTO.getPageNo());
		if (result.getItems().isEmpty()) {
			return new PageResult<>();
		}

		// 收集需要查询的ID列表
		List<String> positionIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getPosition).toList();
		List<String> companyIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getCompany).toList();
		List<String> organizationIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getOrganization)
				.toList();

		// 批量查询关联信息
		List<PostInfoDto> postList = postService.loadPostList(positionIds);
		List<CompanyInfoDto> companyList = companyService.loadCompanyList(companyIds);
		List<OrgInfoDto> orgList = organizeService.loadOrgList(organizationIds);

		// 组装VO
		List<ProjectPositionPageVO> list = Sequences.sequence(result.getItems()).map(data -> {
			ProjectPositionPageVO vo = ObjectConverter.convert(data, ProjectPositionPageVO.class);

			// 组装岗位信息
			Option<PostInfoDto> postOpt = Sequences.sequence(postList)
					.find(post -> data.getPosition().equals(post.getBid()));
			if (postOpt.isDefined()) {
				vo.setPositionTxt(postOpt.get().getName());
				vo.setPositionCode(postOpt.get().getCode());
				vo.setJobTxt(postOpt.get().getJobName());
				vo.setJobGrade(postOpt.get().getJobGrade());
			}

			// 组装公司信息
			Option<CompanyInfoDto> companyOpt = Sequences.sequence(companyList)
					.find(company -> data.getCompany().equals(company.getBid()));
			if (companyOpt.isDefined()) {
				vo.setCompanyTxt(companyOpt.get().getCompanyName());
			}

			// 组装组织信息
			Option<OrgInfoDto> orgOpt = Sequences.sequence(orgList)
					.find(org -> data.getOrganization().equals(org.getBid()));
			if (orgOpt.isDefined()) {
				vo.setOrganizationTxt(orgOpt.get().getOrgName());
			}

			return vo;
		}).toList();
		return new PageResult<>(list, result.getPageNo(), result.getPageSize(), result.getTotal());
	}

	/**
	 * 提交项目岗位变更
	 * 根据draft生成change，保存historyDetail，发起工作流
	 * 无分布式事务，手动回滚
	 *
	 * @param projectId 项目ID
	 */
	public void commitProjectPosition(String projectId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		List<ProjectPosition> supplierList = projectPositionRepository.loadPositionList(projectId);

		// 获取项目岗位相关的所有草稿
		List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.POSITION);
		if (drafts.isEmpty()) {
			log.info("No position drafts found for project: " + projectId);
			return;
		}

		ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(HistoryType.POSITION,
				supplierList, drafts);
		ProjectHistory history = new ProjectHistory(projectId);
		history.setDetailList(Lists.list(historyDetail));

		doCommit(history, WorkflowConfig.PROJECT_MANAGEMENT);

		new ProjectBudgetRefreshEvent(projectId).publish();

	}

	/**
	 *
	 * @param history
	 * @param wc
	 * @return workflow business id
	 */
	private String doCommit(ProjectHistory history, WorkflowConfig wc) {
		try {
			projectHistoryService.saveHistory(history);
		return	workflowService.startWorkflow(history, wc);
		} catch (WorkflowStartException e) {
			// 工作流启动失败，手动回滚已保存的历史记录
			projectHistoryService.rollbackHistory(history);
			throw new ServerException("Failed to start workflow ", e);
		} catch (Exception e) {
			throw new ServerException("Failed to commit ", e);
		}
	}

	public void commitProjectPositionIndividually(String projectId) {
		commitProjectPositionIndividually(projectId, null);
		new ProjectBudgetRefreshEvent(projectId).publish();
	}

	// @PaasTransactional
	public void commitProjectPositionIndividually(String projectId, String positionId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}
		List<ProjectPosition> commitList;
		if (StringUtils.isEmpty(positionId)) {
			commitList = projectPositionRepository.loadPositionList(projectId);
		} else {
			Optional<ProjectPosition> optional = projectPositionRepository.getPosition(positionId);
			if (!optional.isPresent()) {
			}
			commitList = Lists.list(optional.get());
		}

		batchCommitPosition(project, commitList);
	}

	private void batchCommitPosition(Project project, List<ProjectPosition> commitList) {
		List<String> positionIds = Sequences.sequence(commitList).map(AbstractData::getBid).toList();
		List<ProjectDraft> draftList = projectDraftRepository.listByPositionIds(positionIds);
		for (ProjectPosition projectPosition : commitList) {
			Option<ProjectDraft> option = Sequences.sequence(draftList)
					.find(d -> d.getTargetId().equals(projectPosition.getBid()));
			if (option.isEmpty()) {
				throw new ServerException("Position draft not found: " + projectPosition.getBid());
			}
			// todo 加载所有entity
			FormatAllResult result = projectPosition.formatAll(option.get(), draftList);
			String snapshot = result.getSnapshot();
			List<ProjectChange> changes = result.getChanges();
			ProjectHistoryDetail detail = new ProjectHistoryDetail(HistoryType.POSITION);
			detail.setSnapshot(snapshot);
			detail.setChange(changes);
			detail.setDraft(result.getDraftIdList());
			ProjectHistory history = new ProjectHistory(project.getBid(), detail);
			String businessId = doCommit(history, WorkflowConfig.PROJECT_POSITION);
			projectPosition.startWorkflow(businessId);
			projectPositionRepository.saveOrUpdate(projectPosition);
		}
	}

	@PaasTransactional
	public void publish(String positionId) {

		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}
		ProjectPosition position = positionOpt.get();
		if (position.getStatus() != PositionStatus.APPROVED) {
			throw new ServerException("岗位不能发布");
		}

		List<PositionSupplier> supplierList = positionSupplierRepository.getByPositionId(positionId);
		for (PositionSupplier supplier : supplierList) {
			if (supplier.getPublishStatus() == PublishStatus.INIT) {
				supplier.publish();
				positionSupplierRepository.saveOrUpdate(supplier);
			}
		}
		position.publish();
		projectPositionRepository.saveOrUpdate(position);
	}

	@PaasTransactional
	public void supplierPublish(String positionSupplierBid) {
		Optional<PositionSupplier> positionSupplier = positionSupplierRepository.getById(positionSupplierBid);
		if (!positionSupplier.isPresent()) {
			throw new ServerException("岗位供应商关系不存在");
		}
		PositionSupplier ps = positionSupplier.get();
		if (ps.getPublishStatus() != PublishStatus.INIT) {
			throw new ServerException("岗位不能发布");
		}
		ps.publish();
		positionSupplierRepository.saveOrUpdate(ps);
	}

	public void close(String positionId) {
		Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
		if (!positionOpt.isPresent()) {
			throw new ServerException("岗位不存在");
		}
		ProjectPosition position = positionOpt.get();
		if (position.getStatus() != PositionStatus.PUBLISHED) {
			throw new ServerException("岗位不能关闭");
		}
		position.close();
		projectPositionRepository.saveOrUpdate(position);
	}

	public void revoke(String businessKey) {
		String businessType = StringUtils.substringBeforeLast(businessKey, "_");
		WorkflowConfig type = WorkflowConfig.fromCode(businessType);
		if (type != WorkflowConfig.PROJECT_POSITION) {
			throw new ServerException("审批不能撤回");
		}

		Optional<ProjectHistory> projectHistoryOptional = projectHistoryService.loadByBusinessKey(businessKey);
		if (!projectHistoryOptional.isPresent()) {
			throw new ServerException("审批单据不存在");
		}
		ProjectHistory history = projectHistoryOptional.get();
		String positionId = history.getPositionId();
		Optional<ProjectPosition> positionOptional = projectPositionRepository.getPosition(positionId);
		if (!positionOptional.isPresent()) {
			throw new ServerException("岗位不存在");
		}
		ProjectPosition position = positionOptional.get();

		// 撤回流程
		workflowService.revoke(businessKey);

		// 修改状态
		history.setApproveStatus(ApprovalStatus.CANCELLED.toEnumSimple());
		projectHistoryService.saveHistory(history);
		position.revoke();
		projectPositionRepository.saveOrUpdate(position);
	}

	@PaasTransactional
	public void refreshPositionSupplier(String projectId, String supplierId, String positionId) {
		List<ProjectSupplier> supplierList = projectSupplierRepository.findByProjectId(projectId, supplierId);
		List<SupplierContact> contactList = supplierContactRepository
				.loadContactListBySuppliers(Sequences.sequence(supplierList)
						.map(ProjectSupplier::getSupplierId).toList());
		Map<String, SupplierContact> map = new HashMap<>();
		for (SupplierContact contact : contactList) {
			if (String.valueOf(ActiveStatus.INACTIVE.getCode()).equals(contact.getStatus().getValue())) {
				continue;
			}
			String si = contact.getSupplierId();
			map.putIfAbsent(si, contact);
		}

		List<ProjectPosition> positionList = projectPositionRepository.loadPositionList(projectId, positionId);
		for (ProjectSupplier ps : supplierList) {
			Optional<SupplierContact> optional = Optional.ofNullable(map.get(ps.getSupplierId()));
			for (ProjectPosition position : positionList) {
				PositionSupplierDto supplierDto = new PositionSupplierDto();
				supplierDto.setProjectId(projectId);
				supplierDto.setSupplierId(ps.getSupplierId());
				supplierDto.setPositionId(position.getBid());
				optional.ifPresent(
						supplierContact -> supplierDto.setSupplierContact(Lists.list(supplierContact.getBid())));
				addPositionSupplier(supplierDto);
			}
		}
	}

	@PaasTransactional
	public void supplierPublishBatch(String positionId, List<String> bid) {
		List<PositionSupplier> supplierList = positionSupplierRepository.getByPositionId(positionId);
		for (PositionSupplier supplier : supplierList) {
			if (bid.contains(supplier.getBid())) {
				if (supplier.getPublishStatus() != PublishStatus.INIT) {
					throw new ServerException("岗位" + supplier.getSupplierId() + "不能发布");
				}
				supplier.publish();
				positionSupplierRepository.saveOrUpdate(supplier);

			}
		}

	}
}
