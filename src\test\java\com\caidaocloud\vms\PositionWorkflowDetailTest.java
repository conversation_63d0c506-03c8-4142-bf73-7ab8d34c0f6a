package com.caidaocloud.vms;

import com.caidaocloud.vms.application.service.PositionWorkflowService;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 岗位审批流程详情查询测试
 * 
 * <AUTHOR>
 * @date 2025/12/26
 */
@SpringBootTest
@ActiveProfiles("test")
public class PositionWorkflowDetailTest {

    @Autowired
    private PositionWorkflowService positionWorkflowService;

    @Test
    public void testLoadWorkflowPositionDetail() {
        // 测试审批流程岗位详情查询
        try {
            String businessKey = "h123456_VMS_POSITION"; // 示例businessKey
            WorkflowDetailVO<List<ProjectPositionVO>> result = positionWorkflowService.loadWorkflowPositionDetail(businessKey);
            
            System.out.println("岗位审批详情测试结果:");
            System.out.println("历史记录ID: " + result.getHistoryId());
            System.out.println("项目ID: " + result.getProjectId());
            
            if (result.getChanges() != null) {
                System.out.println("变更记录数量: " + result.getChanges().size());
                result.getChanges().forEach(change -> {
                    System.out.println("  - 字段: " + change.getFieldName() + 
                                     ", 旧值: " + change.getOldValue() + 
                                     ", 新值: " + change.getNewValue() + 
                                     ", 操作: " + change.getOperationType());
                });
            }
            
            if (result.getDetail() != null) {
                System.out.println("岗位详情数量: " + result.getDetail().size());
                result.getDetail().forEach(position -> {
                    System.out.println("  - 岗位ID: " + position.getBid());
                    System.out.println("  - 项目ID: " + position.getProjectId());
                    System.out.println("  - 计划人数: " + position.getPlannedHeadcount());
                    System.out.println("  - 实际人数: " + position.getActualHeadcount());
                    System.out.println("  - 工作地点: " + position.getWorkplace());
                    System.out.println("  - 用工类型: " + position.getEmploymentType());
                    System.out.println("  - 是否紧急: " + position.getIsEmergency());
                    System.out.println("  - 薪资范围: " + position.getMinSalary() + " - " + position.getMaxSalary());
                    System.out.println("  ---");
                });
            }
            
        } catch (Exception e) {
            System.out.println("岗位审批详情测试异常（预期的，因为没有真实数据）: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testLoadWorkflowPositionDetailWithSpecificPosition() {
        // 测试查询特定岗位的审批详情
        try {
            String businessKey = "h123456_VMS_POSITION"; // 示例businessKey，包含特定岗位ID
            WorkflowDetailVO<List<ProjectPositionVO>> result = positionWorkflowService.loadWorkflowPositionDetail(businessKey);
            
            System.out.println("特定岗位审批详情测试结果:");
            System.out.println("历史记录ID: " + result.getHistoryId());
            System.out.println("项目ID: " + result.getProjectId());
            
            if (result.getDetail() != null && !result.getDetail().isEmpty()) {
                ProjectPositionVO position = result.getDetail().get(0);
                System.out.println("岗位详情:");
                System.out.println("  - 岗位ID: " + position.getBid());
                System.out.println("  - 开始日期: " + position.getStartDate());
                System.out.println("  - 结束日期: " + position.getEndDate());
                System.out.println("  - 持续时间类型: " + position.getDurationType());
                System.out.println("  - 持续时间(天): " + position.getDuration());
                System.out.println("  - 总预算: " + position.getTotalBudget());
                System.out.println("  - 已用预算: " + position.getUsedBudget());
                
                if (position.getContact() != null) {
                    System.out.println("  - 联系人: " + position.getContact().getName() + 
                                     " (" + position.getContact().getEmpId() + ")");
                }
                
                if (position.getJobDescFiles() != null) {
                    System.out.println("  - 岗位说明书: " + position.getJobDescFiles().getFileName());
                }
            }
            
        } catch (Exception e) {
            System.out.println("特定岗位审批详情测试异常（预期的，因为没有真实数据）: " + e.getMessage());
        }
    }

    @Test
    public void testInvalidBusinessKey() {
        // 测试无效的businessKey
        try {
            String invalidBusinessKey = "invalid_business_key";
            positionWorkflowService.loadWorkflowPositionDetail(invalidBusinessKey);
            
        } catch (Exception e) {
            System.out.println("无效businessKey测试通过，异常信息: " + e.getMessage());
            assert e.getMessage().contains("审批记录不存在");
        }
    }

    @Test
    public void testNonPositionBusinessKey() {
        // 测试非岗位类型的businessKey
        try {
            String nonPositionBusinessKey = "h123456_VMS_PROJECT"; // 项目类型的businessKey
            positionWorkflowService.loadWorkflowPositionDetail(nonPositionBusinessKey);
            
        } catch (Exception e) {
            System.out.println("非岗位类型businessKey测试通过，异常信息: " + e.getMessage());
            assert e.getMessage().contains("该审批记录不包含岗位信息");
        }
    }
}
