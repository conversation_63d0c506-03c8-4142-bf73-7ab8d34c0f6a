package com.caidaocloud.vms.domain.project.history;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/10/11
 */
@Slf4j
public abstract class SimplifiedHistoryFormat<T> extends DataSimpleHistoryFormat<T> {

    @Override
    public List<ProjectChange> format(T originData,ProjectDraft draft) {
        try {
            if (draft == null) {
                return new ArrayList<>();
            }

            // 获取草稿中的操作类型和历史类型
            OperationType operationType = OperationType.fromValue(draft.getOperation());
            HistoryType historyType = HistoryType.fromValue(draft.getType().getValue());
            HistoryType subType = HistoryType.fromValue(draft.getSubType().getValue());

            // 生成简化的变更记录
            return generateSimplifiedChanges(historyType, operationType,subType);
            
        } catch (Exception e) {
            log.error("生成历史格式化失败，targetId: {}", draft.getTargetId(), e);
            return new ArrayList<>();
        }
    }


    public abstract String formatDisplay();

    /**
     * 生成简化的变更记录
     */
    private List<ProjectChange> generateSimplifiedChanges(HistoryType historyType, OperationType operationType, HistoryType subType) {
        try {
            ProjectChange change = new ProjectChange();
            change.setFieldName(historyType.getCode());
            change.setDisplayName(historyType.getDisplay());

            change.setHistoryType(historyType);
            change.setSubType(subType);

            change.setOperationType(operationType);
            change.setOldValue(null);
            change.setNewValue(formatDisplay());
            
            return Arrays.asList(change);
            
        } catch (Exception e) {
            log.error("生成变更记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String formatSummary(List<ProjectChange> changes, OperationType operation) {
        String display = formatDisplay();
        return String.format("%s: %s", operation.getDisplay(), display);
    }
}
