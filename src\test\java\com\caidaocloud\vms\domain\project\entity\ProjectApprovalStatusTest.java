package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目审批状态管理测试类
 * 验证项目在审批中状态时的业务数据保护功能
 *
 * <AUTHOR>
 * @date 2025/12/24
 */
class ProjectApprovalStatusTest {

    private Project project;

    @BeforeEach
    void setUp() {
        project = new Project("TEST-001", "测试项目");
    }

    @Test
    @DisplayName("测试项目提交审批时状态变更")
    void testProjectSubmitWorkflow() {
        // Given
        String workflowBusinessId = "WF_TEST_001";
        assertEquals(ProjectStatus.NEW.getCode(), Integer.parseInt(project.getStatus().getValue()));
        assertNull(project.getWorkflowBusinessId());

        // When
        project.submit(workflowBusinessId);

        // Then
        assertEquals(ProjectStatus.SUBMITTED.getCode(), Integer.parseInt(project.getStatus().getValue()));
        assertEquals(workflowBusinessId, project.getWorkflowBusinessId());
    }

    @Test
    @DisplayName("测试NEW状态项目可以编辑")
    void testNewProjectCanBeEdited() {
        // Given
        project.setStatus(ProjectStatus.NEW.toEnumSimple());

        // When & Then
        assertDoesNotThrow(() -> project.checkUpdate());
    }

    @Test
    @DisplayName("测试APPROVED状态项目可以编辑")
    void testApprovedProjectCanBeEdited() {
        // Given
        project.setStatus(ProjectStatus.APPROVED.toEnumSimple());

        // When & Then
        assertDoesNotThrow(() -> project.checkUpdate());
    }

    @Test
    @DisplayName("测试REJECT状态项目可以编辑")
    void testRejectedProjectCanBeEdited() {
        // Given
        project.setStatus(ProjectStatus.REJECT.toEnumSimple());

        // When & Then
        assertDoesNotThrow(() -> project.checkUpdate());
    }

    @Test
    @DisplayName("测试SUBMITTED状态项目不能编辑")
    void testSubmittedProjectCannotBeEdited() {
        // Given
        project.setStatus(ProjectStatus.SUBMITTED.toEnumSimple());

        // When & Then
        ServerException exception = assertThrows(ServerException.class, () -> project.checkUpdate());
        assertEquals("项目正在审批中，不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试IN_PROGRESS状态项目不能编辑")
    void testInProgressProjectCannotBeEdited() {
        // Given
        project.setStatus(ProjectStatus.IN_PROGRESS.toEnumSimple());

        // When & Then
        ServerException exception = assertThrows(ServerException.class, () -> project.checkUpdate());
        assertEquals("项目不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试CLOSED状态项目不能编辑")
    void testClosedProjectCannotBeEdited() {
        // Given
        project.setStatus(ProjectStatus.CLOSED.toEnumSimple());

        // When & Then
        ServerException exception = assertThrows(ServerException.class, () -> project.checkUpdate());
        assertEquals("项目不可编辑", exception.getMessage());
    }

    @Test
    @DisplayName("测试项目审批通过后状态变更")
    void testProjectApprove() {
        // Given
        project.setStatus(ProjectStatus.SUBMITTED.toEnumSimple());

        // When
        project.approve();

        // Then
        assertEquals(ProjectStatus.APPROVED.getCode(), Integer.parseInt(project.getStatus().getValue()));
    }

    @Test
    @DisplayName("测试项目审批拒绝后状态变更")
    void testProjectReject() {
        // Given
        project.setStatus(ProjectStatus.SUBMITTED.toEnumSimple());

        // When
        project.reject();

        // Then
        assertEquals(ProjectStatus.REJECT.getCode(), Integer.parseInt(project.getStatus().getValue()));
    }
}
