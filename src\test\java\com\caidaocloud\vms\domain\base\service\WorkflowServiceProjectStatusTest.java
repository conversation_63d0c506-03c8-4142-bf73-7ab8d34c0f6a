package com.caidaocloud.vms.domain.base.service;

import com.caidaocloud.vms.application.service.emp.EmpService;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.infrastructure.feign.IWfOperateFeignClient;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * WorkflowService项目状态管理测试类
 * 验证工作流启动时项目状态的正确变更
 *
 * <AUTHOR> Zhou
 * @date 2025/12/24
 */
@ExtendWith(MockitoExtension.class)
class WorkflowServiceProjectStatusTest {

    @Mock
    private IWfOperateFeignClient wfOperateFeignClient;

    @Mock
    private IWfRegisterFeign wfRegisterFeign;

    @Mock
    private EmpService empService;

    @Mock
    private ProjectRepository projectRepository;

    @InjectMocks
    private WorkflowService workflowService;

    private Project project;
    private ProjectHistory projectHistory;

    @BeforeEach
    void setUp() {
        project = new Project("TEST-001", "测试项目");
        project.setBid("project-123");
        project.setStatus(ProjectStatus.NEW.toEnumSimple());

        projectHistory = new ProjectHistory("project-123");
        projectHistory.setBid("history-123");
    }

    @Test
    @DisplayName("测试启动项目管理工作流时更新项目状态为审批中")
    void testStartProjectWorkflowUpdatesStatus() throws Exception {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(project);
        when(wfRegisterFeign.begin(any())).thenReturn(Result.ok());
        when(empService.loadEmp(anyString())).thenReturn(null);

        // When
        workflowService.startWorkflow(projectHistory, WorkflowConfig.PROJECT_MANAGEMENT.getCode());

        // Then
        verify(projectRepository, times(1)).getById("project-123");
        verify(projectRepository, times(1)).saveOrUpdate(project);
        verify(wfRegisterFeign, times(1)).begin(any());
    }

    @Test
    @DisplayName("测试启动非项目管理工作流时不更新项目状态")
    void testStartNonProjectWorkflowDoesNotUpdateStatus() throws Exception {
        // Given
        when(wfRegisterFeign.begin(any())).thenReturn(Result.ok());
        when(empService.loadEmp(anyString())).thenReturn(null);

        // When
        workflowService.startWorkflow(projectHistory, WorkflowConfig.PROJECT_POSITION.getCode());

        // Then
        verify(projectRepository, never()).getById(anyString());
        verify(projectRepository, never()).saveOrUpdate(any());
        verify(wfRegisterFeign, times(1)).begin(any());
    }

    @Test
    @DisplayName("测试项目不存在时不抛出异常")
    void testProjectNotFoundDoesNotThrowException() throws Exception {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(null);
        when(wfRegisterFeign.begin(any())).thenReturn(Result.ok());
        when(empService.loadEmp(anyString())).thenReturn(null);

        // When & Then - 不应该抛出异常
        workflowService.startWorkflow(projectHistory, WorkflowConfig.PROJECT_MANAGEMENT.getCode());

        verify(projectRepository, times(1)).getById("project-123");
        verify(projectRepository, never()).saveOrUpdate(any());
    }

    @Test
    @DisplayName("测试项目状态更新失败时不影响工作流启动")
    void testProjectStatusUpdateFailureDoesNotAffectWorkflow() throws Exception {
        // Given
        when(projectRepository.getById("project-123")).thenReturn(project);
        when(projectRepository.saveOrUpdate(any())).thenThrow(new RuntimeException("Database error"));
        when(wfRegisterFeign.begin(any())).thenReturn(Result.ok());
        when(empService.loadEmp(anyString())).thenReturn(null);

        // When & Then - 不应该抛出异常
        workflowService.startWorkflow(projectHistory, WorkflowConfig.PROJECT_MANAGEMENT.getCode());

        verify(wfRegisterFeign, times(1)).begin(any());
    }

    @Test
    @DisplayName("测试项目ID为空时不更新项目状态")
    void testNullProjectIdDoesNotUpdateStatus() throws Exception {
        // Given
        ProjectHistory historyWithoutProjectId = new ProjectHistory((String) null);
        historyWithoutProjectId.setBid("history-123");
        
        when(wfRegisterFeign.begin(any())).thenReturn(Result.ok());
        when(empService.loadEmp(anyString())).thenReturn(null);

        // When
        workflowService.startWorkflow(historyWithoutProjectId, WorkflowConfig.PROJECT_MANAGEMENT.getCode());

        // Then
        verify(projectRepository, never()).getById(anyString());
        verify(projectRepository, never()).saveOrUpdate(any());
    }
}
