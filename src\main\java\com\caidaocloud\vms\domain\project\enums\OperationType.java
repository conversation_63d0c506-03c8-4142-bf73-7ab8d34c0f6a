package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.apache.commons.lang3.StringUtils;

public enum OperationType {
    CREATE("0", "新增"),
    UPDATE("1", "修改"),
    DELETE("2", "删除"),

    // 数据库不会用到
    COMMIT("3", "提交"),
    CREATE_OR_UPDATE("4","新增或修改")
    ;

    private final String value;
    private final String display;

    OperationType(String value, String display) {
        this.value = value;
        this.display = display;
    }

    public String getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static OperationType fromValue(String value) {
        for (OperationType type : OperationType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

    public static OperationType fromValue(EnumSimple value) {
        if (value == null || StringUtils.isEmpty(value.getValue())) {
            return null;
        }
        try {
            String v = value.getValue();
            for (OperationType type : OperationType.values()) {
                if (type.value.equals(v)) {
                    return type;
                }
            }
            return null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(value);
        return enumSimple;
    }

    public boolean isCreate() {
        return this == CREATE || this == CREATE_OR_UPDATE;
    }
}