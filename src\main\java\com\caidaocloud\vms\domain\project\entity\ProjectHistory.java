package com.caidaocloud.vms.domain.project.entity;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.googlecode.totallylazy.Lists;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProjectHistory extends BaseEntity {

    private String projectId;
    private String positionId;

    private String approveBy;

    private Long approveTime;

    private EnumSimple approveStatus;

    private List<ProjectHistoryDetail> detailList = new ArrayList<>();


    public static String identifier = "entity.vms.ProjectHistory";

    public ProjectHistory(String projectId, ProjectHistoryDetail detail) {
        this(projectId, Lists.list(detail));

    }

    public ProjectHistory(String projectId, List<ProjectHistoryDetail> projectHistoryDetailList) {
        this(projectId);
        detailList = projectHistoryDetailList;
    }

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public ProjectHistory(String projectId) {
        this.projectId = projectId;
        approveStatus = ProjectStatus.NEW.toEnumSimple();
    }
}