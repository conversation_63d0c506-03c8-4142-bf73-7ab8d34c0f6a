package com.caidaocloud.vms.interfaces.manager.facade;

import java.util.List;

import com.caidaocloud.vms.application.service.PositionWorkflowService;
import com.caidaocloud.vms.application.service.WorkflowCallbackService;
import com.caidaocloud.vms.application.vo.PositionRequirementVO;
import com.caidaocloud.vms.application.vo.PositionSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 岗位工作流Controller
 * 处理岗位工作流审批结果的回调和审批单据详情查询
 *
 * <AUTHOR> Zhou
 * @date 2025/10/17
 */
@RestController
@RequestMapping("/api/vms/v1/project/position/workflow")
@Api(tags = "岗位工作流管理")
@Slf4j
public class PositionWorkflowController {

    @Autowired
    private WorkflowCallbackService workflowCallbackService;

    @Autowired
    private PositionWorkflowService positionWorkflowService;

    /**
     * 项目岗位工作流审批通过回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/position/workflow/approve")
    @ApiOperation(value = "项目岗位工作流审批通过", notes = "项目岗位工作流审批通过后的回调处理")
    public Result approveProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {

        log.info("收到项目岗位工作流审批通过回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(),
                WfCallbackTriggerOperationEnum.APPROVED);
        return Result.ok();

    }

    /**
     * 项目岗位工作流审批拒绝回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("reject")
    @ApiOperation(value = "项目岗位工作流审批拒绝", notes = "项目岗位工作流审批拒绝后的回调处理")
    public Result rejectProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {

        log.info("收到项目岗位工作流审批拒绝回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(),
                WfCallbackTriggerOperationEnum.REFUSED);
        return Result.ok();

    }

    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程岗位详情信息
     */
    @GetMapping("detail")
    @ApiOperation(value = "获取审批流程岗位详情", notes = "根据businessKey获取审批流程中的岗位信息详情，包括变更记录")
    public Result<WorkflowDetailVO<ProjectPositionVO>> loadWorkflowPositionDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<ProjectPositionVO> vo = positionWorkflowService.loadWorkflowPositionDetail(businessKey);
        return Result.ok(vo);
    }

    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位招聘要求详情
     *
     * @param businessKey 业务键
     * @return 审批流程岗位详情信息
     */
    @GetMapping("requirement/detail")
    @ApiOperation(value = "获取审批流程岗位详情", notes = "根据businessKey获取审批流程中的岗位信息详情，包括变更记录")
    public Result<WorkflowDetailVO<PositionRequirementVO>> loadWorkflowPositionRequirementDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<PositionRequirementVO> vo = positionWorkflowService.loadWorkflowPositionRequirementDetail(businessKey);
        return Result.ok(vo);
    }


    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位供应商详情
     *
     * @param businessKey 业务键
     * @return 审批流程岗位详情信息
     */
    @GetMapping("supplier/detail")
    @ApiOperation(value = "获取审批流程岗位详情", notes = "根据businessKey获取审批流程中的岗位信息详情，包括变更记录")
    public Result<WorkflowDetailVO<List<PositionSupplierVO>>> loadWorkflowPositionSupplierDetail(
            @ApiParam(value = "业务键", required = true) @RequestParam String businessKey) {

        WorkflowDetailVO<List<PositionSupplierVO>> vo = positionWorkflowService.loadWorkflowPositionSupplierDetail(businessKey);
        return Result.ok(vo);
    }
}
