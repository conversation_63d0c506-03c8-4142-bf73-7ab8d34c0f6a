# 审批流程详情查询接口（分离版本）

## 概述

将原来的单一审批流程详情查询接口拆分成多个专门的接口，每个接口负责特定业务类型的详情查询，并且都包含变更记录。这样设计的好处是：

1. **接口职责单一**：每个接口只负责一种业务类型的数据查询
2. **数据量可控**：避免单个接口返回过多数据
3. **性能优化**：只查询需要的数据，提高响应速度
4. **易于维护**：接口逻辑清晰，便于后续维护和扩展

## 接口列表

### 1. 项目基本信息详情查询

#### 请求地址
```
GET /api/vms/v1/manager/project/workflow/project/detail
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

#### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "submitTime": 1714521600000,
    "approveTime": 1714608000000,
    "changes": [
      {
        "fieldName": "项目名称",
        "oldValue": "旧项目名称",
        "newValue": "新项目名称",
        "operationType": "UPDATE"
      }
    ],
    "projectDetail": {
      "bid": "789012",
      "projectCode": "PRJ-2025-001",
      "projectName": "系统升级项目",
      "startDate": 1714521600000,
      "endDate": 1746057600000,
      "totalBudget": 1000000.00,
      "usedBudget": 500000.00,
      "plannedHeadcount": 10,
      "actualHeadcount": 8,
      "projectManager": {
        "empId": "EMP001",
        "name": "张三"
      },
      "company": "某某公司",
      "remarks": "备注信息",
      "status": "APPROVED"
    }
  }
}
```

### 2. 岗位信息详情查询

#### 请求地址
```
GET /api/vms/v1/manager/project/workflow/position/detail
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

#### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "positionId": "345678",
    "submitTime": 1714521600000,
    "approveTime": 1714608000000,
    "changes": [
      {
        "fieldName": "岗位名称",
        "oldValue": "旧岗位名称",
        "newValue": "新岗位名称",
        "operationType": "UPDATE"
      }
    ],
    "positionDetails": [
      {
        "bid": "345678",
        "projectId": "789012",
        "positionName": "开发工程师",
        "plannedHeadcount": 5,
        "actualHeadcount": 3,
        "workplace": "北京",
        "employmentType": "FULL_TIME"
      }
    ]
  }
}
```

### 3. 供应商信息详情查询

#### 请求地址
```
GET /api/vms/v1/manager/project/workflow/supplier/detail
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

#### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "submitTime": 1714521600000,
    "approveTime": 1714608000000,
    "changes": [
      {
        "fieldName": "供应商名称",
        "oldValue": "旧供应商名称",
        "newValue": "新供应商名称",
        "operationType": "UPDATE"
      }
    ],
    "supplierDetails": [
      {
        "bid": "SUP001",
        "supplierCode": "SUP-001",
        "supplierName": "某某供应商",
        "phone": "010-12345678",
        "address": "北京市朝阳区"
      }
    ]
  }
}
```

### 4. 联系人信息详情查询

#### 请求地址
```
GET /api/vms/v1/manager/project/workflow/contact/detail
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

#### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "submitTime": 1714521600000,
    "approveTime": 1714608000000,
    "changes": [
      {
        "fieldName": "联系人姓名",
        "oldValue": "旧联系人姓名",
        "newValue": "新联系人姓名",
        "operationType": "UPDATE"
      }
    ],
    "contactDetails": [
      {
        "bid": "CON001",
        "projectId": "789012",
        "contact": {
          "empId": "EMP002",
          "name": "李四"
        },
        "email": "<EMAIL>",
        "phone": "***********",
        "organization": "技术部",
        "position": "技术经理"
      }
    ]
  }
}
```

### 5. 设置信息详情查询

#### 请求地址
```
GET /api/vms/v1/manager/project/workflow/setting/detail
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

#### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "submitTime": 1714521600000,
    "approveTime": 1714608000000,
    "changes": [
      {
        "fieldName": "预算强管控",
        "oldValue": false,
        "newValue": true,
        "operationType": "UPDATE"
      }
    ],
    "settingDetail": {
      "bid": "SET001",
      "projectId": "789012",
      "budgetEnabled": true,
      "quoteEnabled": false,
      "headcountEnabled": true,
      "positionAutoClose": false,
      "projectAutoClose": true,
      "positionApprovalFlow": true,
      "preHireEnabled": false
    }
  }
}
```

## 业务逻辑说明

### 1. businessKey解析
- 所有接口都接收格式为 `hhistoryBid_VMS_PROJECT` 的businessKey
- 通过 `ProjectHistoryService.loadByBusinessKey()` 方法查询对应的历史记录

### 2. 业务类型验证
每个接口都会验证对应的业务类型：
- **项目详情接口**: 验证是否包含 `BASIC_INFO` 类型的历史记录
- **岗位详情接口**: 验证是否包含 `POSITION` 类型的历史记录
- **供应商详情接口**: 验证是否包含 `SUPPLIER` 类型的历史记录
- **联系人详情接口**: 验证是否包含 `CONTACT` 类型的历史记录
- **设置详情接口**: 验证是否包含 `SETTING` 类型的历史记录

### 3. 变更记录组装
- 从对应业务类型的 `ProjectHistoryDetail.change` 字段获取变更记录
- 变更记录包含字段名称、旧值、新值、操作类型等信息

### 4. 详情数据查询
根据不同的业务类型，查询对应的详情数据：
- **项目详情**: 直接查询项目基本信息
- **岗位详情**: 从snapshot中解析岗位ID列表并批量查询岗位详情
- **供应商详情**: 从snapshot中解析供应商ID列表并批量查询供应商详情
- **联系人详情**: 从snapshot中解析联系人ID列表并查询联系人详情
- **设置详情**: 根据项目ID查询项目设置信息

## 使用示例

### JavaScript/Ajax调用示例

```javascript
// 查询项目详情
function loadProjectDetail(businessKey) {
    $.ajax({
        url: '/api/vms/v1/manager/project/workflow/project/detail',
        type: 'GET',
        data: { businessKey: businessKey },
        success: function(response) {
            if (response.code === 200) {
                const detail = response.data;
                console.log('项目详情:', detail.projectDetail);
                console.log('变更记录:', detail.changes);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 查询岗位详情
function loadPositionDetail(businessKey) {
    $.ajax({
        url: '/api/vms/v1/manager/project/workflow/position/detail',
        type: 'GET',
        data: { businessKey: businessKey },
        success: function(response) {
            if (response.code === 200) {
                const detail = response.data;
                console.log('岗位详情列表:', detail.positionDetails);
                console.log('变更记录:', detail.changes);
            }
        }
    });
}

// 查询供应商详情
function loadSupplierDetail(businessKey) {
    $.ajax({
        url: '/api/vms/v1/manager/project/workflow/supplier/detail',
        type: 'GET',
        data: { businessKey: businessKey },
        success: function(response) {
            if (response.code === 200) {
                const detail = response.data;
                console.log('供应商详情列表:', detail.supplierDetails);
                console.log('变更记录:', detail.changes);
            }
        }
    });
}
```

### Java调用示例

```java
@Autowired
private ProjectService projectService;

// 查询项目详情
public void getProjectDetail(String businessKey) {
    try {
        WorkflowProjectDetailVO detail = projectService.loadWorkflowProjectDetail(businessKey);
        System.out.println("项目详情: " + detail.getProjectDetail());
        System.out.println("变更记录: " + detail.getChanges());
    } catch (Exception e) {
        log.error("查询项目详情失败", e);
    }
}

// 查询岗位详情
public void getPositionDetail(String businessKey) {
    try {
        WorkflowPositionDetailVO detail = projectService.loadWorkflowPositionDetail(businessKey);
        System.out.println("岗位详情列表: " + detail.getPositionDetails());
        System.out.println("变更记录: " + detail.getChanges());
    } catch (Exception e) {
        log.error("查询岗位详情失败", e);
    }
}

// 查询供应商详情
public void getSupplierDetail(String businessKey) {
    try {
        WorkflowSupplierDetailVO detail = projectService.loadWorkflowSupplierDetail(businessKey);
        System.out.println("供应商详情列表: " + detail.getSupplierDetails());
        System.out.println("变更记录: " + detail.getChanges());
    } catch (Exception e) {
        log.error("查询供应商详情失败", e);
    }
}
```

## 注意事项

1. **businessKey格式**: 必须严格按照 `hhistoryBid_VMS_PROJECT` 格式传入
2. **业务类型验证**: 每个接口只处理对应的业务类型，如果类型不匹配会抛出异常
3. **数据完整性**: 根据不同的业务类型，返回的详情字段可能为空
4. **异常处理**: 当businessKey对应的记录不存在时，会抛出 `ServerException`
5. **性能考虑**: 接口会根据业务类型查询对应的详情数据，避免不必要的数据查询
6. **员工信息**: 当前版本中提交人和审批人信息需要进一步完善（TODO项）

## 相关文件

### Controller层
- `ProjectController.java` - 新增5个分离的接口方法

### Service层
- `ProjectService.java` - 新增5个对应的业务方法

### VO层
- `WorkflowProjectDetailVO.java` - 项目详情响应VO
- `WorkflowPositionDetailVO.java` - 岗位详情响应VO
- `WorkflowSupplierDetailVO.java` - 供应商详情响应VO
- `WorkflowContactDetailVO.java` - 联系人详情响应VO
- `WorkflowSettingDetailVO.java` - 设置详情响应VO

### 测试文件
- `WorkflowDetailSeparatedTest.java` - 分离接口的单元测试

## 优势总结

1. **职责分离**: 每个接口只负责一种业务类型，符合单一职责原则
2. **性能优化**: 按需查询数据，减少不必要的数据传输
3. **易于维护**: 接口逻辑清晰，便于后续维护和扩展
4. **灵活使用**: 前端可以根据需要调用特定的接口
5. **错误隔离**: 某个业务类型的问题不会影响其他类型的查询
