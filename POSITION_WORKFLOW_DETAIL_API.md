# 岗位审批单据详情查询接口

## 概述

参考项目审批单据详情接口的设计，为岗位审批流程提供专门的单据详情查询接口。该接口用于查询岗位审批流程中的详细信息，包括岗位基本信息、变更记录等。

## 接口设计特点

1. **专门化设计**：专门针对岗位审批流程，与项目审批流程分离
2. **完整信息**：包含岗位详细信息和变更记录
3. **灵活查询**：支持查询单个岗位或项目下所有岗位
4. **类型验证**：验证businessKey对应的审批记录是否为岗位类型

## 接口详情

### 请求地址
```
GET /api/vms/v1/project/position/workflow/detail
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_POSITION | h123456_VMS_POSITION |

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "historyId": "123456",
    "projectId": "789012",
    "changes": [
      {
        "fieldName": "计划人数",
        "oldValue": "5",
        "newValue": "8",
        "operationType": "UPDATE"
      },
      {
        "fieldName": "工作地点",
        "oldValue": "北京",
        "newValue": "上海",
        "operationType": "UPDATE"
      }
    ],
    "detail": [
      {
        "bid": "POS001",
        "projectId": "789012",
        "company": "COMP001",
        "organization": "ORG001",
        "position": "POST001",
        "startDate": 1714521600000,
        "endDate": 1746057600000,
        "durationType": "FIXED_PERIOD",
        "duration": 365,
        "totalBudget": 500000,
        "usedBudget": 200000,
        "plannedHeadcount": 8,
        "actualHeadcount": 5,
        "workplace": "上海",
        "contact": {
          "empId": "EMP001",
          "name": "张三"
        },
        "employmentType": "FULL_TIME",
        "isEmergency": false,
        "minSalary": "15000",
        "maxSalary": "25000",
        "jobDescFiles": {
          "fileName": "岗位说明书.pdf",
          "fileUrl": "https://example.com/files/job_desc.pdf"
        }
      }
    ]
  }
}
```

## 业务逻辑说明

### 1. businessKey解析
- 接收格式为 `hhistoryBid_VMS_POSITION` 的businessKey
- 通过 `ProjectHistoryService.loadByBusinessKey()` 方法查询对应的历史记录

### 2. 业务类型验证
- 验证审批记录是否包含 `POSITION` 类型的历史详情
- 如果不是岗位类型的审批记录，抛出异常

### 3. 变更记录组装
- 从岗位类型的 `ProjectHistoryDetail.change` 字段获取变更记录
- 变更记录包含字段名称、旧值、新值、操作类型等信息

### 4. 岗位详情查询
- 如果历史记录中包含具体的岗位ID，则查询单个岗位详情
- 如果没有具体岗位ID，则查询项目下所有岗位列表
- 返回的岗位信息包括基本信息、预算信息、人员信息、联系人信息等

## 使用示例

### JavaScript/Ajax调用示例

```javascript
// 查询岗位审批详情
function loadPositionWorkflowDetail(businessKey) {
    $.ajax({
        url: '/api/vms/v1/project/position/workflow/detail',
        type: 'GET',
        data: { businessKey: businessKey },
        success: function(response) {
            if (response.code === 200) {
                const detail = response.data;
                console.log('岗位审批详情:', detail);
                console.log('历史记录ID:', detail.historyId);
                console.log('项目ID:', detail.projectId);
                console.log('变更记录:', detail.changes);
                console.log('岗位详情列表:', detail.detail);
                
                // 处理岗位列表
                detail.detail.forEach(function(position) {
                    console.log('岗位ID:', position.bid);
                    console.log('计划人数:', position.plannedHeadcount);
                    console.log('实际人数:', position.actualHeadcount);
                    console.log('工作地点:', position.workplace);
                    console.log('用工类型:', position.employmentType);
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}
```

### Java调用示例

```java
@Autowired
private PositionWorkflowService positionWorkflowService;

// 查询岗位审批详情
public void getPositionWorkflowDetail(String businessKey) {
    try {
        WorkflowDetailVO<List<ProjectPositionVO>> detail = 
            positionWorkflowService.loadWorkflowPositionDetail(businessKey);
        
        System.out.println("历史记录ID: " + detail.getHistoryId());
        System.out.println("项目ID: " + detail.getProjectId());
        System.out.println("变更记录: " + detail.getChanges());
        System.out.println("岗位详情列表: " + detail.getDetail());
        
        // 处理岗位列表
        for (ProjectPositionVO position : detail.getDetail()) {
            System.out.println("岗位ID: " + position.getBid());
            System.out.println("计划人数: " + position.getPlannedHeadcount());
            System.out.println("实际人数: " + position.getActualHeadcount());
            System.out.println("工作地点: " + position.getWorkplace());
            System.out.println("用工类型: " + position.getEmploymentType());
        }
        
    } catch (Exception e) {
        log.error("查询岗位审批详情失败", e);
    }
}
```

## 错误处理

### 常见错误情况

1. **审批记录不存在**
   - 错误信息：`审批记录不存在: {businessKey}`
   - 原因：提供的businessKey在系统中找不到对应的历史记录

2. **非岗位类型审批记录**
   - 错误信息：`该审批记录不包含岗位信息: {businessKey}`
   - 原因：businessKey对应的审批记录不是岗位类型

3. **岗位信息不存在**
   - 错误信息：`岗位不存在`
   - 原因：历史记录中的岗位ID在当前系统中已被删除

### 错误响应示例

```json
{
  "code": 500,
  "message": "审批记录不存在: h123456_VMS_POSITION",
  "data": null
}
```

## 注意事项

1. **businessKey格式**: 必须严格按照 `hhistoryBid_VMS_POSITION` 格式传入
2. **业务类型验证**: 只处理岗位类型的审批记录，其他类型会抛出异常
3. **数据完整性**: 根据历史记录中的岗位ID情况，返回单个岗位或岗位列表
4. **异常处理**: 当businessKey对应的记录不存在时，会抛出 `ServerException`
5. **性能考虑**: 接口会根据岗位ID查询对应的详情数据，避免不必要的数据查询

## 与项目审批详情接口的对比

| 特性 | 项目审批详情接口 | 岗位审批详情接口 |
|------|------------------|------------------|
| 请求路径 | `/api/vms/v1/project/workflow/position/detail` | `/api/vms/v1/project/position/workflow/detail` |
| businessKey格式 | `hhistoryBid_VMS_PROJECT` | `hhistoryBid_VMS_POSITION` |
| 验证类型 | `HistoryType.POSITION` | `HistoryType.POSITION` |
| 返回数据 | 项目下所有岗位列表 | 特定岗位或项目下所有岗位 |
| 服务类 | `ProjectWorkflowService` | `PositionWorkflowService` |
| 控制器 | `ProjectWorkflowController` | `PositionWorkflowController` |

## 相关文件

### Controller层
- `PositionWorkflowController.java` - 新增岗位审批详情查询接口

### Service层
- `PositionWorkflowService.java` - 新增岗位工作流服务类

### VO层
- `WorkflowDetailVO.java` - 通用的工作流详情响应VO（泛型）
- `ProjectPositionVO.java` - 岗位详情VO

### 测试文件
- `PositionWorkflowDetailTest.java` - 岗位审批详情接口的单元测试

## 优势总结

1. **专门化设计**: 专门针对岗位审批流程，逻辑清晰
2. **类型安全**: 严格验证业务类型，确保数据正确性
3. **灵活查询**: 支持单个岗位和岗位列表的查询
4. **完整信息**: 包含岗位详细信息和变更记录
5. **错误隔离**: 岗位审批问题不会影响项目审批查询
6. **易于维护**: 代码结构清晰，便于后续维护和扩展

## 扩展建议

1. **缓存优化**: 可以考虑对频繁查询的岗位详情进行缓存
2. **分页支持**: 当岗位数量较多时，可以考虑添加分页功能
3. **字段过滤**: 可以添加字段过滤参数，只返回需要的字段
4. **批量查询**: 可以考虑支持批量查询多个businessKey的功能
