package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "岗位管理分页信息")
public class PositionManagementPageVO {

    @ApiModelProperty(value = "岗位ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "岗位编码")
    private String positionCode;

    @ApiModelProperty(value = "岗位名称")
    private String positionName;

    @ApiModelProperty(value = "公司ID")
    private String company;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "组织ID")
    private String organization;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "开始日期")
    private Long startDate;

    @ApiModelProperty(value = "结束日期")
    private Long endDate;

    @ApiModelProperty(value = "预算总额")
    private Integer totalBudget;

    @ApiModelProperty(value = "已用预算")
    private Integer usedBudget;

    @ApiModelProperty(value = "计划人员数")
    private Integer plannedHeadcount;

    @ApiModelProperty(value = "实际人员数")
    private Integer actualHeadcount;

    @ApiModelProperty(value = "审批状态")
    private PositionStatus status;


    @ApiModelProperty("流程业务id")
    private String businessId;

    @ApiModelProperty(value = "工作地点")
    private String workplace;

    @ApiModelProperty(value = "联系人")
    private EmpSimple contact;

    @ApiModelProperty(value = "项目设置信息")
    private ProjectSettingVO projectSetting;

    @ApiModelProperty(value = "是否需要单独审批")
    private Boolean requiresIndividualApproval;

}
